#!/usr/bin/env python3
"""
Standalone Twitch OAuth authentication setup script for Harmony Bot.
This script will guide you through the OAuth flow to get your Twitch tokens.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from dotenv import load_dotenv
from harmony.twitch.oauth import TwitchOAuth
from harmony.utils.logger import get_logger

# Load environment variables
load_dotenv()

logger = get_logger(__name__)


async def main():
    """Run the Twitch OAuth setup process."""
    print("🎮 Harmony Bot - Twitch OAuth Setup")
    print("=" * 50)
    
    # Check required environment variables
    client_id = os.getenv("TWITCH_CLIENT_ID")
    client_secret = os.getenv("TWITCH_CLIENT_SECRET")
    redirect_uri = os.getenv("TWITCH_REDIRECT_URI", "http://localhost:8080/auth/callback")
    encryption_password = os.getenv("TOKEN_ENCRYPTION_PASSWORD")

    if not client_id or not client_secret:
        print("❌ Missing required Twitch configuration!")
        print("\nPlease ensure your .env file contains:")
        print("  TWITCH_CLIENT_ID=your_client_id_here")
        print("  TWITCH_CLIENT_SECRET=your_client_secret_here")
        print("  TWITCH_REDIRECT_URI=http://localhost:8080/auth/callback")
        print("  TOKEN_ENCRYPTION_PASSWORD=your_encryption_password")
        print("\nTo get these values:")
        print("1. Go to https://dev.twitch.tv/console/apps")
        print("2. Create a new application or use an existing one")
        print("3. Set the OAuth Redirect URL to: http://localhost:8080/auth/callback")
        print("4. Copy the Client ID and Client Secret to your .env file")
        return

    if not encryption_password:
        print("❌ Missing TOKEN_ENCRYPTION_PASSWORD in .env file!")
        print("This is required to securely store your Twitch tokens.")
        return
    
    print(f"✅ Found Twitch configuration:")
    print(f"   Client ID: {client_id[:8]}...")
    print(f"   Redirect URI: {redirect_uri}")
    print()
    
    # Initialize OAuth handler
    oauth = TwitchOAuth(client_id, client_secret, redirect_uri, encryption_password)
    
    # Check for existing tokens
    existing_tokens = await oauth.load_tokens()
    if existing_tokens:
        print("🔍 Found existing tokens, checking validity...")
        is_valid = await oauth.is_token_valid(existing_tokens['access_token'])
        
        if is_valid:
            print("✅ Existing tokens are still valid!")
            user_info = existing_tokens.get('user_info', {})
            username = user_info.get('display_name', user_info.get('login', 'Unknown'))
            print(f"   Authenticated as: {username}")
            print("   No action needed - you're all set!")
            return
        else:
            print("⚠️  Existing tokens are expired, attempting refresh...")
            if existing_tokens.get('refresh_token'):
                refreshed = await oauth.refresh_access_token(existing_tokens['refresh_token'])
                if refreshed:
                    print("✅ Tokens refreshed successfully!")
                    user_info = refreshed.get('user_info', {})
                    username = user_info.get('display_name', user_info.get('login', 'Unknown'))
                    print(f"   Authenticated as: {username}")
                    return
            print("❌ Could not refresh tokens, starting new OAuth flow...")
    
    print("🔐 Starting Twitch OAuth authentication...")
    print("\nThis will:")
    print("1. Start a local web server on port 8080")
    print("2. Open your browser to Twitch's authorization page")
    print("3. After you authorize, redirect back to complete the setup")
    print("4. Save your tokens securely for future use")
    print()
    
    input("Press Enter to continue...")
    
    try:
        # Run the OAuth flow
        success, message = await oauth.authenticate(auto_open_browser=True)
        
        if success:
            print(f"\n🎉 {message}")
            
            # Load and display the new tokens info
            tokens = await oauth.load_tokens()
            if tokens:
                user_info = tokens.get('user_info', {})
                username = user_info.get('display_name', user_info.get('login', 'Unknown'))
                scopes = tokens.get('scope', [])
                
                print(f"✅ Successfully authenticated as: {username}")
                print(f"📋 Granted scopes: {', '.join(scopes) if scopes else 'None'}")
                print(f"💾 Tokens saved to: data/tokens.json")
                print()
                print("🚀 You can now run the Harmony Bot with Twitch integration!")
                print("   Run: python run.py")
        else:
            print(f"\n❌ Authentication failed: {message}")
            print("\nTroubleshooting:")
            print("1. Make sure your Twitch app's OAuth Redirect URL is set to:")
            print(f"   {redirect_uri}")
            print("2. Check that your Client ID and Secret are correct")
            print("3. Ensure port 8080 is not blocked by firewall")
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Authentication cancelled by user")
    except Exception as e:
        print(f"\n❌ Error during authentication: {e}")
        logger.error(f"OAuth setup error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
