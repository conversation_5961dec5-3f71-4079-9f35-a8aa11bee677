"""
Twitch OAuth authentication flow implementation.
"""

import asyncio
import aiohttp
import secrets
import urllib.parse
import os
from typing import Dict, Optional, Tu<PERSON>
from datetime import datetime, timedelta
import webbrowser
from aiohttp import web
import json
from pathlib import Path

from ..utils.logger import get_logger
from ..utils.security import SecurityManager

logger = get_logger(__name__)


class TwitchOAuth:
    """Handles Twitch OAuth authentication flow."""
    
    def __init__(self, client_id: str, client_secret: str, redirect_uri: str, encryption_password: str = None):
        self.client_id = client_id
        self.client_secret = client_secret
        self.redirect_uri = redirect_uri

        # Get encryption password from environment or parameter
        self.encryption_password = encryption_password or os.getenv("TOKEN_ENCRYPTION_PASSWORD", "default_password")
        self.security_manager = SecurityManager(self.encryption_password)

        # OAuth state management
        self._state = None
        self._auth_server = None
        self._auth_result = None

        # Token storage
        self.tokens_file = Path("data/tokens.json")
        self.tokens_file.parent.mkdir(parents=True, exist_ok=True)
    
    def generate_auth_url(self, scopes: list = None) -> str:
        """
        Generate Twitch OAuth authorization URL.
        
        Args:
            scopes: List of OAuth scopes to request
            
        Returns:
            Authorization URL
        """
        if scopes is None:
            scopes = [
                'chat:read',
                'chat:edit', 
                'channel:read:subscriptions',
                'bits:read',
                'channel:read:redemptions',
                'moderator:read:followers'
            ]
        
        # Generate secure state parameter
        self._state = secrets.token_urlsafe(32)
        
        params = {
            'client_id': self.client_id,
            'redirect_uri': self.redirect_uri,
            'response_type': 'code',
            'scope': ' '.join(scopes),
            'state': self._state
        }
        
        base_url = 'https://id.twitch.tv/oauth2/authorize'
        return f"{base_url}?{urllib.parse.urlencode(params)}"
    
    async def start_auth_server(self, port: int = 8080) -> None:
        """Start local server to handle OAuth callback."""
        app = web.Application()
        app.router.add_get('/auth/callback', self._handle_callback)
        app.router.add_get('/', self._handle_root)
        
        runner = web.AppRunner(app)
        await runner.setup()
        
        site = web.TCPSite(runner, 'localhost', port)
        await site.start()
        
        self._auth_server = runner
        logger.info(f"OAuth callback server started on http://localhost:{port}")
    
    async def stop_auth_server(self) -> None:
        """Stop the OAuth callback server."""
        if self._auth_server:
            await self._auth_server.cleanup()
            self._auth_server = None
            logger.info("OAuth callback server stopped")
    
    async def _handle_root(self, request) -> web.Response:
        """Handle root path requests."""
        html = """
        <html>
        <head><title>Harmony Bot OAuth</title></head>
        <body>
            <h1>Harmony Bot OAuth Setup</h1>
            <p>Please complete the authorization in the Twitch tab.</p>
            <p>This page will update when authorization is complete.</p>
        </body>
        </html>
        """
        return web.Response(text=html, content_type='text/html')
    
    async def _handle_callback(self, request) -> web.Response:
        """Handle OAuth callback from Twitch."""
        try:
            # Extract parameters
            code = request.query.get('code')
            state = request.query.get('state')
            error = request.query.get('error')
            
            if error:
                self._auth_result = {'error': error, 'description': request.query.get('error_description')}
                return web.Response(text=f"Authorization failed: {error}", status=400)
            
            if not code or not state:
                self._auth_result = {'error': 'missing_parameters', 'description': 'Missing code or state parameter'}
                return web.Response(text="Missing required parameters", status=400)
            
            if state != self._state:
                self._auth_result = {'error': 'invalid_state', 'description': 'State parameter mismatch'}
                return web.Response(text="Invalid state parameter", status=400)
            
            # Exchange code for tokens
            tokens = await self._exchange_code_for_tokens(code)
            
            if tokens:
                # Validate and store tokens
                user_info = await self._get_user_info(tokens['access_token'])
                if user_info:
                    tokens['user_info'] = user_info
                    await self._store_tokens(tokens)
                    
                    self._auth_result = {'success': True, 'tokens': tokens}
                    
                    html = f"""
                    <html>
                    <head><title>Authorization Successful</title></head>
                    <body>
                        <h1>✅ Authorization Successful!</h1>
                        <p>Welcome, {user_info.get('display_name', 'User')}!</p>
                        <p>You can now close this window and return to the Harmony Bot.</p>
                        <script>setTimeout(() => window.close(), 3000);</script>
                    </body>
                    </html>
                    """
                    return web.Response(text=html, content_type='text/html')
            
            self._auth_result = {'error': 'token_exchange_failed', 'description': 'Failed to exchange code for tokens'}
            return web.Response(text="Failed to complete authorization", status=500)
            
        except Exception as e:
            logger.error(f"OAuth callback error: {e}")
            self._auth_result = {'error': 'callback_error', 'description': str(e)}
            return web.Response(text="Authorization error occurred", status=500)
    
    async def _exchange_code_for_tokens(self, code: str) -> Optional[Dict]:
        """Exchange authorization code for access tokens."""
        try:
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'code': code,
                'grant_type': 'authorization_code',
                'redirect_uri': self.redirect_uri
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://id.twitch.tv/oauth2/token',
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        tokens = await response.json()
                        tokens['expires_at'] = (
                            datetime.now() + timedelta(seconds=tokens.get('expires_in', 3600))
                        ).isoformat()
                        return tokens
                    else:
                        error_data = await response.json()
                        logger.error(f"Token exchange failed: {error_data}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error exchanging code for tokens: {e}")
            return None
    
    async def _get_user_info(self, access_token: str) -> Optional[Dict]:
        """Get user information using access token."""
        try:
            headers = {
                'Authorization': f'Bearer {access_token}',
                'Client-Id': self.client_id
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'https://api.twitch.tv/helix/users',
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        data = await response.json()
                        users = data.get('data', [])
                        return users[0] if users else None
                    else:
                        logger.error(f"Failed to get user info: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error getting user info: {e}")
            return None
    
    async def _store_tokens(self, tokens: Dict) -> None:
        """Store tokens securely."""
        try:
            # Encrypt sensitive data
            encrypted_tokens = {
                'access_token': self.security_manager.encrypt(tokens['access_token']),
                'refresh_token': self.security_manager.encrypt(tokens.get('refresh_token', '')),
                'expires_at': tokens['expires_at'],
                'scope': tokens.get('scope', []),
                'user_info': tokens.get('user_info', {}),
                'created_at': datetime.now().isoformat(),
                'salt': self.security_manager.get_salt()  # Store the salt for decryption
            }

            with open(self.tokens_file, 'w') as f:
                json.dump(encrypted_tokens, f, indent=2)

            logger.info("Tokens stored securely")

        except Exception as e:
            logger.error(f"Error storing tokens: {e}")
    
    async def load_tokens(self) -> Optional[Dict]:
        """Load stored tokens."""
        try:
            if not self.tokens_file.exists():
                return None

            with open(self.tokens_file, 'r') as f:
                encrypted_tokens = json.load(f)

            # Check if we have a stored salt and use it
            if 'salt' in encrypted_tokens:
                # Create a new security manager with the stored salt
                from ..utils.security import SecurityManager
                security_manager = SecurityManager.from_salt(self.encryption_password, encrypted_tokens['salt'])
            else:
                # Fallback to current security manager (for backward compatibility)
                security_manager = self.security_manager

            # Decrypt tokens
            tokens = {
                'access_token': security_manager.decrypt(encrypted_tokens['access_token']),
                'refresh_token': security_manager.decrypt(encrypted_tokens.get('refresh_token', '')),
                'expires_at': encrypted_tokens['expires_at'],
                'scope': encrypted_tokens.get('scope', []),
                'user_info': encrypted_tokens.get('user_info', {}),
                'created_at': encrypted_tokens.get('created_at')
            }

            return tokens

        except Exception as e:
            logger.error(f"Error loading tokens: {e}")
            return None
    
    async def refresh_access_token(self, refresh_token: str) -> Optional[Dict]:
        """Refresh access token using refresh token."""
        try:
            data = {
                'client_id': self.client_id,
                'client_secret': self.client_secret,
                'grant_type': 'refresh_token',
                'refresh_token': refresh_token
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    'https://id.twitch.tv/oauth2/token',
                    data=data,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    if response.status == 200:
                        tokens = await response.json()
                        tokens['expires_at'] = (
                            datetime.now() + timedelta(seconds=tokens.get('expires_in', 3600))
                        ).isoformat()
                        await self._store_tokens(tokens)
                        return tokens
                    else:
                        logger.error(f"Token refresh failed: {response.status}")
                        return None
                        
        except Exception as e:
            logger.error(f"Error refreshing token: {e}")
            return None
    
    async def is_token_valid(self, access_token: str) -> bool:
        """Check if access token is still valid."""
        try:
            headers = {
                'Authorization': f'OAuth {access_token}'
            }
            
            async with aiohttp.ClientSession() as session:
                async with session.get(
                    'https://id.twitch.tv/oauth2/validate',
                    headers=headers,
                    timeout=aiohttp.ClientTimeout(total=10)
                ) as response:
                    return response.status == 200
                    
        except Exception as e:
            logger.error(f"Error validating token: {e}")
            return False
    
    async def authenticate(self, auto_open_browser: bool = True) -> Tuple[bool, str]:
        """
        Complete OAuth authentication flow.
        
        Args:
            auto_open_browser: Whether to automatically open browser
            
        Returns:
            Tuple of (success, message)
        """
        try:
            # Check for existing valid tokens
            existing_tokens = await self.load_tokens()
            if existing_tokens:
                is_valid = await self.is_token_valid(existing_tokens['access_token'])
                if is_valid:
                    return True, "Using existing valid tokens"
                elif existing_tokens.get('refresh_token'):
                    # Try to refresh
                    refreshed = await self.refresh_access_token(existing_tokens['refresh_token'])
                    if refreshed:
                        return True, "Tokens refreshed successfully"
            
            # Start OAuth flow
            await self.start_auth_server()
            auth_url = self.generate_auth_url()
            
            print(f"\n🔐 Twitch OAuth Authentication Required")
            print(f"Please visit this URL to authorize Harmony Bot:")
            print(f"{auth_url}\n")
            
            if auto_open_browser:
                webbrowser.open(auth_url)
            
            # Wait for callback
            timeout = 300  # 5 minutes
            for _ in range(timeout):
                if self._auth_result:
                    break
                await asyncio.sleep(1)
            
            await self.stop_auth_server()
            
            if self._auth_result:
                if self._auth_result.get('success'):
                    return True, "Authentication successful"
                else:
                    error = self._auth_result.get('error', 'unknown')
                    description = self._auth_result.get('description', '')
                    return False, f"Authentication failed: {error} - {description}"
            else:
                return False, "Authentication timed out"
                
        except Exception as e:
            logger.error(f"Authentication error: {e}")
            return False, f"Authentication error: {str(e)}"
