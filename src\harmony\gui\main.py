"""
Main GUI module for Harmony AI Twitch Co-Host Bot.

This module provides a desktop interface for configuring and monitoring the bot.
"""

import asyncio
import threading
import tkinter as tk
from typing import Optional, Dict, Any
from pathlib import Path

try:
    import customtkinter as ctk
    GUI_AVAILABLE = True
except ImportError:
    GUI_AVAILABLE = False
    ctk = None

from ..config.manager import ConfigManager
from ..config.settings import HarmonyConfig
from ..core.bot import HarmonyBot
from ..utils.logger import get_logger

logger = get_logger(__name__)


class HarmonyGUI:
    """Main GUI application for Harmony bot."""
    
    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize the GUI application.
        
        Args:
            config_path: Optional path to configuration file
        """
        if not GUI_AVAILABLE:
            raise ImportError(
                "GUI dependencies not available. "
                "Install with: pip install customtkinter pillow"
            )
        
        self.config_path = config_path
        self.config_manager = ConfigManager(config_path)
        self.config: Optional[HarmonyConfig] = None
        self.bot: Optional[HarmonyBot] = None
        self.bot_thread: Optional[threading.Thread] = None
        self.bot_running = False
        
        # Initialize GUI
        self._setup_gui()
        self._load_config()
    
    def _setup_gui(self):
        """Setup the main GUI window and components."""
        # Set appearance mode and color theme
        ctk.set_appearance_mode("dark")
        ctk.set_default_color_theme("blue")
        
        # Create main window
        self.root = ctk.CTk()
        self.root.title("Harmony AI Twitch Co-Host Bot")
        self.root.geometry("1200x800")
        self.root.minsize(800, 600)
        
        # Create main frame
        self.main_frame = ctk.CTkFrame(self.root)
        self.main_frame.pack(fill="both", expand=True, padx=20, pady=20)
        
        # Create title
        self.title_label = ctk.CTkLabel(
            self.main_frame,
            text="🤖 Harmony AI Twitch Co-Host Bot",
            font=ctk.CTkFont(size=24, weight="bold")
        )
        self.title_label.pack(pady=(20, 30))
        
        # Create status frame
        self._create_status_frame()
        
        # Create control buttons
        self._create_control_frame()
        
        # Create configuration tabs
        self._create_config_tabs()
        
        # Setup window close handler
        self.root.protocol("WM_DELETE_WINDOW", self._on_closing)
    
    def _create_status_frame(self):
        """Create the status display frame."""
        self.status_frame = ctk.CTkFrame(self.main_frame)
        self.status_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Status label
        self.status_label = ctk.CTkLabel(
            self.status_frame,
            text="Status: Stopped",
            font=ctk.CTkFont(size=16, weight="bold")
        )
        self.status_label.pack(pady=10)
        
        # Status indicator
        self.status_indicator = ctk.CTkLabel(
            self.status_frame,
            text="●",
            font=ctk.CTkFont(size=20),
            text_color="red"
        )
        self.status_indicator.pack()
    
    def _create_control_frame(self):
        """Create the control buttons frame."""
        self.control_frame = ctk.CTkFrame(self.main_frame)
        self.control_frame.pack(fill="x", padx=20, pady=(0, 20))
        
        # Start/Stop button
        self.start_stop_button = ctk.CTkButton(
            self.control_frame,
            text="Start Bot",
            command=self._toggle_bot,
            font=ctk.CTkFont(size=14, weight="bold"),
            height=40
        )
        self.start_stop_button.pack(side="left", padx=(20, 10), pady=20)
        
        # Reload config button
        self.reload_button = ctk.CTkButton(
            self.control_frame,
            text="Reload Config",
            command=self._reload_config,
            height=40
        )
        self.reload_button.pack(side="left", padx=10, pady=20)
        
        # Open logs button
        self.logs_button = ctk.CTkButton(
            self.control_frame,
            text="View Logs",
            command=self._open_logs,
            height=40
        )
        self.logs_button.pack(side="left", padx=10, pady=20)
    
    def _create_config_tabs(self):
        """Create the configuration tabs."""
        self.tab_view = ctk.CTkTabview(self.main_frame)
        self.tab_view.pack(fill="both", expand=True, padx=20, pady=(0, 20))
        
        # Create tabs
        self.tab_view.add("General")
        self.tab_view.add("Twitch")
        self.tab_view.add("AI")
        self.tab_view.add("Audio")
        self.tab_view.add("Features")
        
        # Populate tabs (basic implementation)
        self._create_general_tab()
        self._create_twitch_tab()
        self._create_ai_tab()
        self._create_audio_tab()
        self._create_features_tab()
    
    def _create_general_tab(self):
        """Create the general configuration tab."""
        tab = self.tab_view.tab("General")
        
        # Bot name
        ctk.CTkLabel(tab, text="Bot Name:").pack(anchor="w", padx=20, pady=(20, 5))
        self.bot_name_entry = ctk.CTkEntry(tab, placeholder_text="Harmony")
        self.bot_name_entry.pack(fill="x", padx=20, pady=(0, 15))
        
        # Debug mode
        self.debug_var = ctk.BooleanVar()
        self.debug_checkbox = ctk.CTkCheckBox(
            tab, 
            text="Debug Mode", 
            variable=self.debug_var
        )
        self.debug_checkbox.pack(anchor="w", padx=20, pady=5)
    
    def _create_twitch_tab(self):
        """Create the Twitch configuration tab."""
        tab = self.tab_view.tab("Twitch")
        
        # Placeholder for Twitch settings
        ctk.CTkLabel(
            tab, 
            text="Twitch Configuration",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=20)
        
        ctk.CTkLabel(
            tab, 
            text="Configure your Twitch settings here.\nThis will be implemented in a future update.",
            justify="center"
        ).pack(pady=10)
    
    def _create_ai_tab(self):
        """Create the AI configuration tab."""
        tab = self.tab_view.tab("AI")
        
        # Placeholder for AI settings
        ctk.CTkLabel(
            tab, 
            text="AI Configuration",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=20)
        
        ctk.CTkLabel(
            tab, 
            text="Configure your AI settings here.\nThis will be implemented in a future update.",
            justify="center"
        ).pack(pady=10)
    
    def _create_audio_tab(self):
        """Create the audio configuration tab."""
        tab = self.tab_view.tab("Audio")
        
        # Placeholder for audio settings
        ctk.CTkLabel(
            tab, 
            text="Audio Configuration",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=20)
        
        ctk.CTkLabel(
            tab, 
            text="Configure your audio settings here.\nThis will be implemented in a future update.",
            justify="center"
        ).pack(pady=10)
    
    def _create_features_tab(self):
        """Create the features configuration tab."""
        tab = self.tab_view.tab("Features")
        
        # Placeholder for feature settings
        ctk.CTkLabel(
            tab, 
            text="Feature Configuration",
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=20)
        
        ctk.CTkLabel(
            tab, 
            text="Configure bot features here.\nThis will be implemented in a future update.",
            justify="center"
        ).pack(pady=10)
    
    def _load_config(self):
        """Load configuration from file."""
        try:
            self.config = self.config_manager.load_config()
            self._update_gui_from_config()
            logger.info("Configuration loaded successfully")
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            # Use default config
            self.config = HarmonyConfig(
                twitch={"client_id": "", "client_secret": "", "bot_username": "", "streamer_username": ""},
                ai={"gemini_api_key": "", "elevenlabs_api_key": ""},
                security={"secret_key": "", "token_encryption_password": ""}
            )
    
    def _update_gui_from_config(self):
        """Update GUI elements from loaded configuration."""
        if self.config:
            self.bot_name_entry.delete(0, tk.END)
            self.bot_name_entry.insert(0, self.config.personality.name)
            self.debug_var.set(self.config.debug)
    
    def _toggle_bot(self):
        """Start or stop the bot."""
        if not self.bot_running:
            self._start_bot()
        else:
            self._stop_bot()
    
    def _start_bot(self):
        """Start the bot in a separate thread."""
        try:
            self.bot = HarmonyBot(self.config_path)
            self.bot_thread = threading.Thread(target=self._run_bot_async, daemon=True)
            self.bot_thread.start()
            
            self.bot_running = True
            self.start_stop_button.configure(text="Stop Bot")
            self.status_label.configure(text="Status: Starting...")
            self.status_indicator.configure(text_color="yellow")
            
            logger.info("Bot started")
        except Exception as e:
            logger.error(f"Failed to start bot: {e}")
            self._show_error("Failed to start bot", str(e))
    
    def _stop_bot(self):
        """Stop the bot."""
        try:
            if self.bot:
                # Note: This is a simplified stop mechanism
                # In a real implementation, you'd want proper async shutdown
                self.bot_running = False
                
            self.start_stop_button.configure(text="Start Bot")
            self.status_label.configure(text="Status: Stopped")
            self.status_indicator.configure(text_color="red")
            
            logger.info("Bot stopped")
        except Exception as e:
            logger.error(f"Failed to stop bot: {e}")
            self._show_error("Failed to stop bot", str(e))
    
    def _run_bot_async(self):
        """Run the bot asynchronously in the thread."""
        try:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            loop.run_until_complete(self.bot.run())
        except Exception as e:
            logger.error(f"Bot runtime error: {e}")
        finally:
            self.bot_running = False
            # Update GUI in main thread
            self.root.after(0, self._on_bot_stopped)
    
    def _on_bot_stopped(self):
        """Handle bot stopped event in main thread."""
        self.start_stop_button.configure(text="Start Bot")
        self.status_label.configure(text="Status: Stopped")
        self.status_indicator.configure(text_color="red")
    
    def _reload_config(self):
        """Reload configuration from file."""
        self._load_config()
        logger.info("Configuration reloaded")
    
    def _open_logs(self):
        """Open the logs directory."""
        logs_path = Path("data/logs")
        if logs_path.exists():
            import subprocess
            import sys
            
            if sys.platform == "win32":
                subprocess.run(["explorer", str(logs_path)])
            elif sys.platform == "darwin":
                subprocess.run(["open", str(logs_path)])
            else:
                subprocess.run(["xdg-open", str(logs_path)])
        else:
            self._show_error("Logs not found", "Log directory does not exist yet.")
    
    def _show_error(self, title: str, message: str):
        """Show an error dialog."""
        error_window = ctk.CTkToplevel(self.root)
        error_window.title(title)
        error_window.geometry("400x200")
        error_window.transient(self.root)
        error_window.grab_set()
        
        ctk.CTkLabel(
            error_window, 
            text=title,
            font=ctk.CTkFont(size=16, weight="bold")
        ).pack(pady=(20, 10))
        
        ctk.CTkLabel(error_window, text=message, wraplength=350).pack(pady=10)
        
        ctk.CTkButton(
            error_window, 
            text="OK", 
            command=error_window.destroy
        ).pack(pady=20)
    
    def _on_closing(self):
        """Handle window closing event."""
        if self.bot_running:
            self._stop_bot()
        self.root.destroy()
    
    def run(self):
        """Start the GUI application."""
        logger.info("Starting Harmony GUI...")
        self.root.mainloop()


def launch_gui(config_path: Optional[str] = None):
    """
    Launch the Harmony GUI application.
    
    Args:
        config_path: Optional path to configuration file
    """
    try:
        app = HarmonyGUI(config_path)
        app.run()
    except ImportError as e:
        logger.error(f"GUI dependencies not available: {e}")
        logger.info("Install GUI dependencies with: pip install customtkinter pillow")
        raise
    except Exception as e:
        logger.error(f"Failed to launch GUI: {e}")
        raise


if __name__ == "__main__":
    launch_gui()
