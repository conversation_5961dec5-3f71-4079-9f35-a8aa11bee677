#!/usr/bin/env python3
"""
Manual Twitch OAuth authentication setup for Harmony Bot.
This version doesn't require a local web server - you'll manually copy the authorization code.
"""

import asyncio
import sys
import os
import urllib.parse
import secrets
from pathlib import Path

# Add src directory to Python path
src_path = Path(__file__).parent / "src"
sys.path.insert(0, str(src_path))

from dotenv import load_dotenv
import aiohttp
from harmony.utils.security import SecurityManager
from harmony.utils.logger import get_logger
import json
from datetime import datetime, timedelta

# Load environment variables
load_dotenv()

logger = get_logger(__name__)


class ManualTwitchOAuth:
    """Manual Twitch OAuth that doesn't require a local server."""
    
    def __init__(self, client_id: str, client_secret: str, encryption_password: str):
        self.client_id = client_id
        self.client_secret = client_secret
        self.encryption_password = encryption_password
        self.security_manager = SecurityManager(encryption_password)
        
        # Token storage
        self.tokens_file = Path("data/tokens.json")
        self.tokens_file.parent.mkdir(parents=True, exist_ok=True)
    
    def generate_auth_url(self) -> tuple[str, str]:
        """Generate authorization URL and state."""
        scopes = [
            'chat:read',
            'chat:edit', 
            'channel:read:subscriptions',
            'bits:read',
            'channel:read:redemptions',
            'moderator:read:followers'
        ]
        
        # Generate secure state parameter
        state = secrets.token_urlsafe(32)
        
        params = {
            'client_id': self.client_id,
            'redirect_uri': 'http://localhost:8080/auth/callback',  # Match your Twitch app config
            'response_type': 'code',
            'scope': ' '.join(scopes),
            'state': state
        }
        
        base_url = 'https://id.twitch.tv/oauth2/authorize'
        auth_url = f"{base_url}?{urllib.parse.urlencode(params)}"
        
        return auth_url, state
    
    async def exchange_code_for_tokens(self, code: str) -> dict:
        """Exchange authorization code for tokens."""
        data = {
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'code': code,
            'grant_type': 'authorization_code',
            'redirect_uri': 'http://localhost:8080/auth/callback'
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(
                'https://id.twitch.tv/oauth2/token',
                data=data,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    tokens = await response.json()
                    tokens['expires_at'] = (
                        datetime.now() + timedelta(seconds=tokens.get('expires_in', 3600))
                    ).isoformat()
                    return tokens
                else:
                    error_data = await response.json()
                    raise Exception(f"Token exchange failed: {error_data}")
    
    async def get_user_info(self, access_token: str) -> dict:
        """Get user information."""
        headers = {
            'Authorization': f'Bearer {access_token}',
            'Client-Id': self.client_id
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.get(
                'https://api.twitch.tv/helix/users',
                headers=headers,
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    users = data.get('data', [])
                    return users[0] if users else {}
                else:
                    raise Exception(f"Failed to get user info: {response.status}")
    
    async def store_tokens(self, tokens: dict) -> None:
        """Store tokens securely."""
        # Encrypt sensitive data
        encrypted_tokens = {
            'access_token': self.security_manager.encrypt(tokens['access_token']),
            'refresh_token': self.security_manager.encrypt(tokens.get('refresh_token', '')),
            'expires_at': tokens['expires_at'],
            'scope': tokens.get('scope', []),
            'user_info': tokens.get('user_info', {}),
            'created_at': datetime.now().isoformat()
        }
        
        with open(self.tokens_file, 'w') as f:
            json.dump(encrypted_tokens, f, indent=2)
        
        logger.info("Tokens stored securely")
    
    async def load_tokens(self) -> dict:
        """Load stored tokens."""
        if not self.tokens_file.exists():
            return None
        
        with open(self.tokens_file, 'r') as f:
            encrypted_tokens = json.load(f)
        
        # Decrypt tokens
        tokens = {
            'access_token': self.security_manager.decrypt(encrypted_tokens['access_token']),
            'refresh_token': self.security_manager.decrypt(encrypted_tokens.get('refresh_token', '')),
            'expires_at': encrypted_tokens['expires_at'],
            'scope': encrypted_tokens.get('scope', []),
            'user_info': encrypted_tokens.get('user_info', {}),
            'created_at': encrypted_tokens.get('created_at')
        }
        
        return tokens


async def main():
    """Run the manual Twitch OAuth setup."""
    print("🎮 Harmony Bot - Manual Twitch OAuth Setup")
    print("=" * 50)
    
    # Check required environment variables
    client_id = os.getenv("TWITCH_CLIENT_ID")
    client_secret = os.getenv("TWITCH_CLIENT_SECRET")
    encryption_password = os.getenv("TOKEN_ENCRYPTION_PASSWORD")
    
    if not client_id or not client_secret or not encryption_password:
        print("❌ Missing required configuration!")
        print("\nPlease ensure your .env file contains:")
        print("  TWITCH_CLIENT_ID=your_client_id_here")
        print("  TWITCH_CLIENT_SECRET=your_client_secret_here")
        print("  TOKEN_ENCRYPTION_PASSWORD=your_encryption_password")
        return
    
    print(f"✅ Found Twitch configuration:")
    print(f"   Client ID: {client_id[:8]}...")
    print()
    
    # Initialize OAuth handler
    oauth = ManualTwitchOAuth(client_id, client_secret, encryption_password)
    
    # Check for existing tokens
    existing_tokens = await oauth.load_tokens()
    if existing_tokens:
        print("✅ Found existing tokens!")
        user_info = existing_tokens.get('user_info', {})
        username = user_info.get('display_name', user_info.get('login', 'Unknown'))
        print(f"   Authenticated as: {username}")
        print("   No action needed - you're all set!")
        return
    
    print("🔐 Manual OAuth Setup Process:")
    print("1. We'll generate an authorization URL")
    print("2. You'll visit it in your browser and authorize the app")
    print("3. Copy the authorization code from the URL")
    print("4. Paste it here to complete the setup")
    print()
    
    # Generate authorization URL
    auth_url, state = oauth.generate_auth_url()
    
    print("📋 Step 1: Visit this URL in your browser:")
    print(f"{auth_url}")
    print()
    print("📋 Step 2: After authorizing, you'll be redirected to a page that won't load.")
    print("   That's normal! Look at the URL in your browser's address bar.")
    print("   It will look like: http://localhost:8080/auth/callback?code=XXXXXXXX&scope=...")
    print()
    print("📋 Step 3: Copy the 'code' parameter from that URL and paste it below.")
    print("   (It's the long string after 'code=' and before '&scope')")
    print()
    
    # Get authorization code from user
    auth_code = input("🔑 Paste the authorization code here: ").strip()
    
    if not auth_code:
        print("❌ No authorization code provided. Exiting.")
        return
    
    try:
        print("\n🔄 Exchanging code for tokens...")
        
        # Exchange code for tokens
        tokens = await oauth.exchange_code_for_tokens(auth_code)
        
        # Get user info
        user_info = await oauth.get_user_info(tokens['access_token'])
        tokens['user_info'] = user_info
        
        # Store tokens
        await oauth.store_tokens(tokens)
        
        print("🎉 Success! Twitch OAuth setup complete!")
        print(f"✅ Authenticated as: {user_info.get('display_name', 'Unknown')}")
        print(f"📋 Granted scopes: {', '.join(tokens.get('scope', []))}")
        print(f"💾 Tokens saved securely to: {oauth.tokens_file}")
        print()
        print("🚀 You can now run Harmony Bot with Twitch integration:")
        print("   python run.py")
        
    except Exception as e:
        print(f"❌ Error during setup: {e}")
        logger.error(f"OAuth setup error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
